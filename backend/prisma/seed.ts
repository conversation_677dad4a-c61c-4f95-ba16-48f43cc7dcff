// Country Seeder for India
async function seedCountryIndia() {
    console.log('🌱 Seeding country: India...');
    const existing = await prisma.country.findFirst({ where: { name: 'India' } });
    if (existing) {
        console.log('⏭️  Country India already exists. Skipping.');
        return existing;
    }
    const india = await prisma.country.create({
        data: {
            name: 'India',
            iso2: 'IN',
            iso3: 'IND',
            phoneCode: '+91',
            currency: 'INR',
        },
    });
    console.log('✅ Country India created');
    return india;
}

// Vehicle Document Seeder for India
async function seedVehicleDocumentsIndia() {
    console.log('🌱 Seeding vehicle documents for India...');
    const india = await prisma.country.findFirst({ where: { name: 'India' } });
    if (!india) {
        throw new Error('Country India must exist before seeding vehicle documents');
    }
    const docs = [
        { name: 'Vehicle Registration', identifier: 'vehicle_registration' },
        { name: 'NOC', identifier: 'noc' },
        { name: 'Insurance', identifier: 'insurance' },
    ];
    for (const doc of docs) {
        // Try to find existing document by identifier and countryId
        const existing = await prisma.vehicleDocument.findFirst({
            where: { identifier: doc.identifier, countryId: india.id },
        });
        if (existing) {
            await prisma.vehicleDocument.update({
                where: { id: existing.id },
                data: {
                    name: doc.name,
                    identifier: doc.identifier,
                    countryId: india.id,
                },
            });
        } else {
            await prisma.vehicleDocument.create({
                data: {
                    name: doc.name,
                    identifier: doc.identifier,
                    countryId: india.id,
                },
            });
        }
    }
    console.log('✅ Vehicle documents for India seeded');
}
// Vehicle Seeder
async function seedVehicles() {
    console.log('🌱 Seeding vehicles...');

    const existingVehiclesCount = await prisma.vehicleType.count();
    if (existingVehiclesCount > 0) {
        console.log(`⏭️  Vehicles already exist (${existingVehiclesCount} found). Skipping vehicle seeding.`);
        return;
    }

    const vehicles = [
        {
            name: 'Sedan',
            description: 'Car',
            image: null,
        },
        {
            name: 'Motorbike/Scooter',
            description: 'Two-wheeler',
            image: null,
        },
        {
            name: 'Auto Rickshaw',
            description: 'Three-wheeler',
            image: null,
        },
    ];

    const createdVehicles = await prisma.vehicleType.createMany({
        data: vehicles,
        skipDuplicates: true,
    });

    console.log(`✅ Created ${createdVehicles.count} vehicles`);
}

// KYC Documents Seeder for India
async function seedKycDocumentsIndia() {
    console.log('🌱 Seeding KYC documents for India...');

    // Get India country
    const india = await prisma.country.findFirst({ where: { name: 'India' } });
    if (!india) {
        console.log('❌ India country not found. Cannot seed KYC documents.');
        return;
    }

    const existingKycDocsCount = await prisma.kycDocument.count({ where: { countryId: india.id } });
    if (existingKycDocsCount > 0) {
        console.log(`⏭️  KYC documents for India already exist (${existingKycDocsCount} found). Skipping KYC document seeding.`);
        return;
    }

    const kycDocuments = [
        {
            countryId: india.id,
            name: 'Aadhaar Card',
            identifier: 'aadhaar_card',
            requiredFields: {
                fields: ['aadhaar_number', 'name', 'address', 'date_of_birth']
            },
            isMandatory: true,
        },
        {
            countryId: india.id,
            name: 'Driving Licence',
            identifier: 'driving_licence',
            requiredFields: {
                fields: ['licence_number', 'name', 'address', 'date_of_birth', 'expiry_date', 'vehicle_class']
            },
            isMandatory: true,
        },
        {
            countryId: india.id,
            name: 'Profile Photo',
            identifier: 'profile_photo',
            requiredFields: {
                fields: ['photo_url', 'upload_date']
            },
            isMandatory: false,
        },
        {
            countryId: india.id,
            name: 'Bank Details',
            identifier: 'bank_details',
            requiredFields: {
                fields: ['account_number', 'ifsc_code', 'account_holder_name', 'bank_name']
            },
            isMandatory: false,
        },
        {
            countryId: india.id,
            name: 'Police Clearance Certificate',
            identifier: 'police_clearance_certificate',
            requiredFields: {
                fields: ['certificate_number', 'issue_date', 'expiry_date', 'issuing_authority']
            },
            isMandatory: false,
        },
    ];

    const createdKycDocuments = await prisma.kycDocument.createMany({
        data: kycDocuments,
        skipDuplicates: true,
    });

    console.log(`✅ Created ${createdKycDocuments.count} KYC documents for India`);
}

// Language Seeder
async function seedLanguages() {
    console.log('🌱 Seeding languages...');

    const existingLanguagesCount = await prisma.language.count();
    if (existingLanguagesCount > 0) {
        console.log(`⏭️  Languages already exist (${existingLanguagesCount} found). Skipping language seeding.`);
        return;
    }

    const languages = [
        {
            code: 'en',
            name: 'English',
            nameInNative: 'English',
        },
        {
            code: 'ml',
            name: 'Malayalam',
            nameInNative: 'മലയാളം',
        },
        {
            code: 'hi',
            name: 'Hindi',
            nameInNative: 'हिन्दी',
        },
    ];

    const createdLanguages = await prisma.language.createMany({
        data: languages,
        skipDuplicates: true,
    });

    console.log(`✅ Created ${createdLanguages.count} languages`);
}
import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

interface PermissionData {
    name: string;
    description: string;
    resource: string;
    action: string;
}

interface RoleData {
    name: string;
    description: string;
    permissions: string[]; // Array of permission names
}

// Define all permissions for the system
const permissions: PermissionData[] = [
    // User Management Permissions
    { name: 'users:create', description: 'Create new users', resource: 'users', action: 'create' },
    { name: 'users:read', description: 'View user information', resource: 'users', action: 'read' },
    { name: 'users:update', description: 'Update user information', resource: 'users', action: 'update' },
    { name: 'users:delete', description: 'Delete users', resource: 'users', action: 'delete' },
    { name: 'users:list', description: 'List all users', resource: 'users', action: 'list' },

    // Role Management Permissions
    { name: 'roles:create', description: 'Create new roles', resource: 'roles', action: 'create' },
    { name: 'roles:read', description: 'View role information', resource: 'roles', action: 'read' },
    { name: 'roles:update', description: 'Update role information', resource: 'roles', action: 'update' },
    { name: 'roles:delete', description: 'Delete roles', resource: 'roles', action: 'delete' },
    { name: 'roles:list', description: 'List all roles', resource: 'roles', action: 'list' },
    { name: 'roles:assign', description: 'Assign roles to users', resource: 'roles', action: 'assign' },

    // Permission Management Permissions
    { name: 'permissions:create', description: 'Create new permissions', resource: 'permissions', action: 'create' },
    { name: 'permissions:read', description: 'View permission information', resource: 'permissions', action: 'read' },
    { name: 'permissions:update', description: 'Update permission information', resource: 'permissions', action: 'update' },
    { name: 'permissions:delete', description: 'Delete permissions', resource: 'permissions', action: 'delete' },
    { name: 'permissions:list', description: 'List all permissions', resource: 'permissions', action: 'list' },
    { name: 'permissions:assign', description: 'Assign permissions to roles', resource: 'permissions', action: 'assign' },

    // Ride Management Permissions
    { name: 'rides:create', description: 'Create new rides', resource: 'rides', action: 'create' },
    { name: 'rides:read', description: 'View ride information', resource: 'rides', action: 'read' },
    { name: 'rides:update', description: 'Update ride information', resource: 'rides', action: 'update' },
    { name: 'rides:delete', description: 'Delete rides', resource: 'rides', action: 'delete' },
    { name: 'rides:list', description: 'List rides', resource: 'rides', action: 'list' },
    { name: 'rides:accept', description: 'Accept ride requests', resource: 'rides', action: 'accept' },
    { name: 'rides:cancel', description: 'Cancel rides', resource: 'rides', action: 'cancel' },
    { name: 'rides:complete', description: 'Complete rides', resource: 'rides', action: 'complete' },
    { name: 'rides:rate', description: 'Rate rides', resource: 'rides', action: 'rate' },

    // Driver Management Permissions
    { name: 'drivers:create', description: 'Create driver profiles', resource: 'drivers', action: 'create' },
    { name: 'drivers:read', description: 'View driver information', resource: 'drivers', action: 'read' },
    { name: 'drivers:update', description: 'Update driver information', resource: 'drivers', action: 'update' },
    { name: 'drivers:delete', description: 'Delete driver profiles', resource: 'drivers', action: 'delete' },
    { name: 'drivers:list', description: 'List all drivers', resource: 'drivers', action: 'list' },
    { name: 'drivers:approve', description: 'Approve driver applications', resource: 'drivers', action: 'approve' },
    { name: 'drivers:suspend', description: 'Suspend driver accounts', resource: 'drivers', action: 'suspend' },
    { name: 'drivers:verify', description: 'Verify driver documents', resource: 'drivers', action: 'verify' },

    // Rider Management Permissions
    { name: 'riders:create', description: 'Create rider profiles', resource: 'riders', action: 'create' },
    { name: 'riders:read', description: 'View rider information', resource: 'riders', action: 'read' },
    { name: 'riders:update', description: 'Update rider information', resource: 'riders', action: 'update' },
    { name: 'riders:delete', description: 'Delete rider profiles', resource: 'riders', action: 'delete' },
    { name: 'riders:list', description: 'List all riders', resource: 'riders', action: 'list' },
    { name: 'riders:suspend', description: 'Suspend rider accounts', resource: 'riders', action: 'suspend' },

    // Vehicle Management Permissions
    { name: 'vehicles:create', description: 'Create vehicle records', resource: 'vehicles', action: 'create' },
    { name: 'vehicles:read', description: 'View vehicle information', resource: 'vehicles', action: 'read' },
    { name: 'vehicles:update', description: 'Update vehicle information', resource: 'vehicles', action: 'update' },
    { name: 'vehicles:delete', description: 'Delete vehicle records', resource: 'vehicles', action: 'delete' },
    { name: 'vehicles:list', description: 'List all vehicles', resource: 'vehicles', action: 'list' },
    { name: 'vehicles:approve', description: 'Approve vehicle registrations', resource: 'vehicles', action: 'approve' },

    // Payment Management Permissions
    { name: 'payments:create', description: 'Process payments', resource: 'payments', action: 'create' },
    { name: 'payments:read', description: 'View payment information', resource: 'payments', action: 'read' },
    { name: 'payments:update', description: 'Update payment information', resource: 'payments', action: 'update' },
    { name: 'payments:refund', description: 'Process refunds', resource: 'payments', action: 'refund' },
    { name: 'payments:list', description: 'List all payments', resource: 'payments', action: 'list' },

    // Location Management Permissions
    { name: 'locations:create', description: 'Create location records', resource: 'locations', action: 'create' },
    { name: 'locations:read', description: 'View location information', resource: 'locations', action: 'read' },
    { name: 'locations:update', description: 'Update location information', resource: 'locations', action: 'update' },
    { name: 'locations:delete', description: 'Delete location records', resource: 'locations', action: 'delete' },
    { name: 'locations:list', description: 'List all locations', resource: 'locations', action: 'list' },

    // City Management Permissions
    { name: 'cities:create', description: 'Create city records', resource: 'cities', action: 'create' },
    { name: 'cities:read', description: 'View city information', resource: 'cities', action: 'read' },
    { name: 'cities:update', description: 'Update city information', resource: 'cities', action: 'update' },
    { name: 'cities:delete', description: 'Delete city records', resource: 'cities', action: 'delete' },
    { name: 'cities:list', description: 'List all cities', resource: 'cities', action: 'list' },

    // Country Management Permissions
    { name: 'countries:create', description: 'Create country records', resource: 'countries', action: 'create' },
    { name: 'countries:read', description: 'View country information', resource: 'countries', action: 'read' },
    { name: 'countries:update', description: 'Update country information', resource: 'countries', action: 'update' },
    { name: 'countries:delete', description: 'Delete country records', resource: 'countries', action: 'delete' },
    { name: 'countries:list', description: 'List all countries', resource: 'countries', action: 'list' },

    // Report Management Permissions
    { name: 'reports:create', description: 'Generate reports', resource: 'reports', action: 'create' },
    { name: 'reports:read', description: 'View reports', resource: 'reports', action: 'read' },
    { name: 'reports:export', description: 'Export reports', resource: 'reports', action: 'export' },

    // System Management Permissions
    { name: 'system:settings', description: 'Manage system settings', resource: 'system', action: 'settings' },
    { name: 'system:health', description: 'View system health', resource: 'system', action: 'health' },
    { name: 'system:logs', description: 'View system logs', resource: 'system', action: 'logs' },
    { name: 'system:backup', description: 'Create system backups', resource: 'system', action: 'backup' },

    // Profile Management Permissions (self)
    { name: 'profile:read', description: 'View own profile', resource: 'profile', action: 'read' },
    { name: 'profile:update', description: 'Update own profile', resource: 'profile', action: 'update' },
    { name: 'profile:delete', description: 'Delete own account', resource: 'profile', action: 'delete' },
];

// Define roles and their permissions
const roles: RoleData[] = [
    {
        name: 'super_admin',
        description: 'Super administrator with full system access',
        permissions: permissions.map(p => p.name), // All permissions
    },
    {
        name: 'rider',
        description: 'Regular rider user',
        permissions: [
            // Profile management
            'profile:read',
            'profile:update',
            'profile:delete',

            // Ride management (rider specific)
            'rides:create',
            'rides:read',
            'rides:cancel',
            'rides:rate',

            // View locations and cities
            'locations:read',
            'locations:list',
            'cities:read',
            'cities:list',
            'countries:read',
            'countries:list',

            // Payment management (own payments)
            'payments:read',
            'payments:create',

            // Driver information (for ride matching)
            'drivers:read',
        ],
    },
    {
        name: 'driver',
        description: 'Driver user with vehicle and ride management capabilities',
        permissions: [
            // Profile management
            'profile:read',
            'profile:update',
            'profile:delete',

            // Ride management (driver specific)
            'rides:read',
            'rides:accept',
            'rides:update',
            'rides:complete',
            'rides:cancel',
            'rides:rate',
            'rides:list',

            // Vehicle management (own vehicles)
            'vehicles:create',
            'vehicles:read',
            'vehicles:update',
            'vehicles:list',

            // View locations and cities
            'locations:read',
            'locations:list',
            'cities:read',
            'cities:list',
            'countries:read',
            'countries:list',

            // Payment management (own earnings)
            'payments:read',
            'payments:list',

            // Rider information (for ride completion)
            'riders:read',
        ],
    },
];

async function seedPermissions() {
    console.log('🌱 Seeding permissions...');

    const existingPermissionsCount = await prisma.permission.count();
    if (existingPermissionsCount > 0) {
        console.log(`⏭️  Permissions already exist (${existingPermissionsCount} found). Skipping permission seeding.`);
        return;
    }

    const createdPermissions = await prisma.permission.createMany({
        data: permissions,
        skipDuplicates: true,
    });

    console.log(`✅ Created ${createdPermissions.count} permissions`);
}

async function seedRoles() {
    console.log('🌱 Seeding roles...');

    const existingRolesCount = await prisma.role.count();
    if (existingRolesCount > 0) {
        console.log(`⏭️  Roles already exist (${existingRolesCount} found). Skipping role seeding.`);
        return;
    }

    for (const roleData of roles) {
        // Create the role
        const role = await prisma.role.create({
            data: {
                name: roleData.name,
                description: roleData.description,
            },
        });

        console.log(`📝 Created role: ${role.name}`);

        // Get all permissions that should be assigned to this role
        const permissionsToAssign = await prisma.permission.findMany({
            where: {
                name: {
                    in: roleData.permissions,
                },
            },
        });

        if (permissionsToAssign.length !== roleData.permissions.length) {
            console.warn(`⚠️  Warning: Expected ${roleData.permissions.length} permissions for role ${roleData.name}, but found ${permissionsToAssign.length}`);
        }

        // Create role-permission relationships
        const rolePermissions = permissionsToAssign.map(permission => ({
            roleId: role.id,
            permissionId: permission.id,
        }));

        await prisma.rolePermission.createMany({
            data: rolePermissions,
        });

        console.log(`🔐 Assigned ${permissionsToAssign.length} permissions to role: ${role.name}`);
    }

    console.log('✅ Roles seeding completed');
}

// Superadmin Seeder
async function seedSuperAdmin() {
    console.log('🔑 Seeding superadmin user...');

    const adminEmail = '<EMAIL>';
    const adminPassword = 'tukxi@2025';

    // Check if superadmin already exists
    const existingAdmin = await prisma.user.findFirst({
        where: { email: adminEmail },
    });

    if (existingAdmin) {
        console.log('⏭️  Superadmin already exists. Skipping.');
        return existingAdmin;
    }

    // Find super_admin role
    const superAdminRole = await prisma.role.findFirst({
        where: { name: 'super_admin' },
    });

    if (!superAdminRole) {
        throw new Error('Super admin role not found. Please run role seeding first.');
    }

    // Create superadmin user
    const adminUser = await prisma.user.create({
        data: {
            email: adminEmail,
            emailVerifiedAt: new Date(),
            phoneNumber: null,
            phoneVerifiedAt: null,
            otpSecret: null,
            isPolicyAllowed: true,
        },
    });

    // Hash password and create auth credential
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(adminPassword, saltRounds);

    await prisma.authCredential.create({
        data: {
            type: 'PASSWORD',
            identifier: hashedPassword,
            userId: adminUser.id,
            metadata: {
                createdFor: 'superadmin',
                createdAt: new Date().toISOString(),
            },
        },
    });

    // Assign super_admin role to user
    await prisma.userRole.create({
        data: {
            userId: adminUser.id,
            roleId: superAdminRole.id,
        },
    });

    console.log(`✅ Superadmin created with email: ${adminEmail}`);
    console.log(`🔐 Password: ${adminPassword}`);

    return adminUser;
}

async function main() {
    console.log('🚀 Starting database seeding...');

    try {
        // Always seed country India and vehicle documents for India first
        await seedCountryIndia();
        await seedVehicleDocumentsIndia();
        await seedKycDocumentsIndia();
        await seedSuperAdmin(); // Seed superadmin after roles are created

        // Check if seeding is needed for the rest
        const existingRolesCount = await prisma.role.count();
        const existingPermissionsCount = await prisma.permission.count();
        const existingLanguagesCount = await prisma.language.count();
        const existingVehiclesCount = await prisma.vehicleType.count();

        if (existingRolesCount > 0 && existingPermissionsCount > 0 && existingLanguagesCount > 0 && existingVehiclesCount > 0) {
            console.log('🛑 Database already seeded. Skipping seeding process.');
            console.log(`   Found ${existingRolesCount} roles, languages ${existingLanguagesCount} and ${existingPermissionsCount} permissions.`);
            console.log(`   Found ${existingVehiclesCount} Vehicle Types.`);
            return;
        }

        await seedPermissions();
        await seedRoles();
        await seedLanguages();
        await seedVehicles();

        console.log('🎉 Database seeding completed successfully!');

        // Print summary
        const finalRolesCount = await prisma.role.count();
        const finalPermissionsCount = await prisma.permission.count();
        const finalRolePermissionsCount = await prisma.rolePermission.count();
        const finalLanguagesCount = await prisma.language.count();

        console.log('\n📊 Seeding Summary:');
        console.log(`   Permissions: ${finalPermissionsCount}`);
        console.log(`   Roles: ${finalRolesCount}`);
        console.log(`   Role-Permission associations: ${finalRolePermissionsCount}`);
        console.log(`   Languages: ${finalLanguagesCount}`);

    } catch (error) {
        console.error('❌ Error during seeding:', error);
        throw error;
    }
}

main()
    .catch((e) => {
        console.error('💥 Seeding failed:', e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });

// Export the main function for potential reuse
export { main as seedDatabase };
